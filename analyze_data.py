#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据结构分析脚本
用于分析裁判文书数据的格式和结构
"""

import os
import zipfile
import pandas as pd
import json
from pathlib import Path

def analyze_nested_zip(zip_path, max_depth=2, current_depth=0):
    """递归分析嵌套的压缩文件"""
    if current_depth >= max_depth:
        return None

    print(f"{'  ' * current_depth}分析文件: {zip_path}")

    try:
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            file_list = zip_ref.namelist()
            print(f"{'  ' * current_depth}压缩包内文件数量: {len(file_list)}")

            # 查找CSV文件
            csv_files = [f for f in file_list if f.endswith('.csv')]
            if csv_files:
                return analyze_csv_in_zip(zip_ref, csv_files[0], zip_path)

            # 如果没有CSV文件，查找嵌套的zip文件
            nested_zips = [f for f in file_list if f.endswith('.zip') and not f.startswith('__MACOSX')]
            if nested_zips and current_depth < max_depth - 1:
                print(f"{'  ' * current_depth}找到嵌套压缩文件: {nested_zips[:3]}")

                # 提取第一个嵌套zip文件并分析
                first_nested = nested_zips[0]
                temp_path = f"temp_nested_{current_depth}.zip"

                try:
                    with zip_ref.open(first_nested) as nested_file:
                        with open(temp_path, 'wb') as temp_file:
                            temp_file.write(nested_file.read())

                    result = analyze_nested_zip(temp_path, max_depth, current_depth + 1)
                    os.remove(temp_path)  # 清理临时文件
                    return result

                except Exception as e:
                    print(f"{'  ' * current_depth}处理嵌套文件时出错: {e}")
                    if os.path.exists(temp_path):
                        os.remove(temp_path)

            print(f"{'  ' * current_depth}未找到CSV文件")

    except Exception as e:
        print(f"{'  ' * current_depth}分析文件时出错: {e}")

    return None

def analyze_csv_in_zip(zip_ref, csv_filename, zip_path):
    """分析压缩包中的CSV文件"""
    print(f"\n分析CSV文件: {csv_filename}")

    try:
        with zip_ref.open(csv_filename) as csv_file:
            # 尝试不同的编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
            df = None

            for encoding in encodings:
                try:
                    csv_file.seek(0)
                    df = pd.read_csv(csv_file, encoding=encoding, nrows=5)
                    print(f"成功使用编码: {encoding}")
                    break
                except Exception as e:
                    print(f"编码 {encoding} 失败: {str(e)[:100]}")
                    continue

            if df is not None:
                print(f"\n数据形状: {df.shape}")
                print("\n列名:")
                for i, col in enumerate(df.columns):
                    print(f"  {i+1}. {col}")

                print("\n前3行数据:")
                print(df.head(3).to_string())

                print("\n数据类型:")
                print(df.dtypes)

                return {
                    'file_path': zip_path,
                    'csv_file': csv_filename,
                    'columns': list(df.columns),
                    'sample_data': df.head(3).to_dict('records'),
                    'data_types': df.dtypes.to_dict()
                }
            else:
                print("无法读取CSV文件")

    except Exception as e:
        print(f"分析CSV文件时出错: {e}")

    return None

def main():
    """主函数"""
    print("开始分析裁判文书数据结构...")
    
    # 数据目录
    data_dir = Path("裁判文书")
    
    # 收集所有zip文件
    zip_files = []
    for root, dirs, files in os.walk(data_dir):
        for file in files:
            if file.endswith('.zip'):
                zip_files.append(os.path.join(root, file))
    
    print(f"找到 {len(zip_files)} 个压缩文件")
    
    # 分析前几个文件
    analysis_results = []
    for i, zip_file in enumerate(zip_files[:3]):  # 只分析前3个文件
        result = analyze_nested_zip(zip_file)
        if result:
            analysis_results.append(result)
    
    # 保存分析结果
    if analysis_results:
        with open('data_analysis_result.json', 'w', encoding='utf-8') as f:
            json.dump(analysis_results, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"\n分析完成！结果已保存到 data_analysis_result.json")
        
        # 总结分析结果
        print("\n=== 数据结构总结 ===")
        all_columns = set()
        for result in analysis_results:
            all_columns.update(result['columns'])
        
        print(f"发现的所有字段 ({len(all_columns)} 个):")
        for i, col in enumerate(sorted(all_columns), 1):
            print(f"  {i}. {col}")

if __name__ == "__main__":
    main()
