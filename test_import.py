#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据导入脚本
用于测试小规模数据导入功能
"""

import os
import sys
import subprocess
from pathlib import Path

def find_first_zip_file(data_dir="裁判文书"):
    """找到第一个ZIP文件进行测试"""
    for root, dirs, files in os.walk(data_dir):
        for file in files:
            if file.endswith('.zip'):
                return os.path.join(root, file)
    return None

def test_single_file_import():
    """测试单个文件导入"""
    print("开始测试单个文件导入...")
    
    # 找到第一个ZIP文件
    test_file = find_first_zip_file()
    if not test_file:
        print("未找到测试文件")
        return False
    
    print(f"测试文件: {test_file}")
    
    # 创建临时测试目录
    test_dir = "test_data"
    os.makedirs(test_dir, exist_ok=True)
    
    # 复制测试文件到临时目录
    import shutil
    test_file_copy = os.path.join(test_dir, os.path.basename(test_file))
    shutil.copy2(test_file, test_file_copy)
    
    try:
        # 运行导入脚本
        cmd = [sys.executable, 'data_importer.py', 
               '--data-dir', test_dir, 
               '--workers', '1']
        
        print("执行命令:", ' '.join(cmd))
        
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        print("返回码:", result.returncode)
        print("输出:")
        print(result.stdout)
        
        if result.stderr:
            print("错误:")
            print(result.stderr)
        
        return result.returncode == 0
        
    finally:
        # 清理临时目录
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)

def check_database_content():
    """检查数据库内容"""
    print("\n检查数据库内容...")
    
    try:
        from pymongo import MongoClient
        
        client = MongoClient('mongodb://localhost:27017/')
        db = client['judicial_documents']
        
        # 检查文档数量
        doc_count = db['documents'].count_documents({})
        print(f"文档总数: {doc_count}")
        
        # 检查进度记录
        progress_count = db['import_progress'].count_documents({})
        print(f"进度记录数: {progress_count}")
        
        # 显示最近的几个文档
        if doc_count > 0:
            print("\n最近的文档:")
            recent_docs = list(db['documents'].find().limit(3))
            for i, doc in enumerate(recent_docs, 1):
                print(f"{i}. 案号: {doc.get('案号', 'N/A')}")
                print(f"   地区: {doc.get('所属地区', 'N/A')}")
                print(f"   类型: {doc.get('案件类型', 'N/A')}")
                print(f"   导入时间: {doc.get('导入时间', 'N/A')}")
                print()
        
        # 显示进度记录
        if progress_count > 0:
            print("进度记录:")
            progress_docs = list(db['import_progress'].find().limit(3))
            for i, doc in enumerate(progress_docs, 1):
                print(f"{i}. 文件: {doc.get('file_name', 'N/A')}")
                print(f"   状态: {doc.get('status', 'N/A')}")
                print(f"   处理记录数: {doc.get('processed_records', 0)}")
                print()
        
        client.close()
        return True
        
    except Exception as e:
        print(f"检查数据库失败: {e}")
        return False

def test_api_endpoints():
    """测试API端点"""
    print("\n测试API端点...")
    
    try:
        import requests
        
        base_url = "http://localhost:5000"
        
        endpoints = [
            "/api/statistics",
            "/api/progress", 
            "/api/documents/count",
            "/health"
        ]
        
        for endpoint in endpoints:
            try:
                response = requests.get(f"{base_url}{endpoint}", timeout=5)
                print(f"✓ {endpoint}: {response.status_code}")
                
                if endpoint == "/api/statistics":
                    data = response.json()
                    print(f"  总文档数: {data.get('total_documents', 0)}")
                elif endpoint == "/api/documents/count":
                    data = response.json()
                    print(f"  文档数量: {data.get('count', 0)}")
                    
            except Exception as e:
                print(f"✗ {endpoint}: {e}")
        
        return True
        
    except Exception as e:
        print(f"测试API失败: {e}")
        return False

def main():
    """主函数"""
    print("="*60)
    print("裁判文书导入系统测试")
    print("="*60)
    
    # 检查MongoDB连接
    try:
        from pymongo import MongoClient
        client = MongoClient('mongodb://localhost:27017/', serverSelectionTimeoutMS=3000)
        client.admin.command('ping')
        client.close()
        print("✓ MongoDB连接正常")
    except Exception as e:
        print(f"✗ MongoDB连接失败: {e}")
        return False
    
    # 测试单个文件导入
    if test_single_file_import():
        print("✓ 单个文件导入测试成功")
    else:
        print("✗ 单个文件导入测试失败")
        return False
    
    # 检查数据库内容
    if check_database_content():
        print("✓ 数据库内容检查完成")
    else:
        print("✗ 数据库内容检查失败")
    
    # 测试API端点
    if test_api_endpoints():
        print("✓ API端点测试完成")
    else:
        print("✗ API端点测试失败")
    
    print("\n" + "="*60)
    print("测试完成！")
    print("="*60)
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
