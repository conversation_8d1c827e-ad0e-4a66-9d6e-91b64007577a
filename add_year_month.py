#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为现有数据添加年月信息脚本
"""

import re
from pymongo import MongoClient
from datetime import datetime
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def extract_year_from_case_number(case_number):
    """从案号中提取年份信息"""
    if not case_number:
        return None

    # 案号格式通常是：（2021）京01民终8315号
    match = re.search(r'[（(](\d{4})[）)]', case_number)
    if match:
        year = int(match.group(1))
        if 2000 <= year <= 2030:
            return year

    return None

def extract_year_month_from_filename(filename):
    """从文件名中提取年月信息"""
    if not filename:
        return None, None

    # 尝试多种模式匹配年月
    patterns = [
        r'(\d{4})年(\d{1,2})月',  # 2021年01月
        r'(\d{4})年?(\d{1,2})',   # 2021年1 或 20211
        r'(\d{4})-(\d{1,2})',     # 2021-01
        r'(\d{4})_(\d{1,2})',     # 2021_01
        r'(\d{4})\.(\d{1,2})',    # 2021.01
        r'(\d{4})(\d{2})',        # 202101
    ]

    for pattern in patterns:
        match = re.search(pattern, filename)
        if match:
            year = int(match.group(1))
            month = int(match.group(2))
            if 2000 <= year <= 2030 and 1 <= month <= 12:
                return year, month

    # 如果没有找到月份，尝试只匹配年份
    year_match = re.search(r'(\d{4})', filename)
    if year_match:
        year = int(year_match.group(1))
        if 2000 <= year <= 2030:
            return year, None

    return None, None

def add_year_month_to_existing_data():
    """为现有数据添加年月信息"""
    try:
        client = MongoClient('mongodb://localhost:27017/')
        db = client['judicial_documents']
        
        print("开始为现有数据添加年月信息...")
        
        # 获取所有没有年月信息的文档
        query = {
            '$or': [
                {'数据年份': {'$exists': False}},
                {'数据年份': None}
            ]
        }
        
        total_docs = db['documents'].count_documents(query)
        print(f"需要更新的文档数: {total_docs}")
        
        if total_docs == 0:
            print("所有文档都已有年月信息")
            return True
        
        # 分批处理文档，从案号中提取年份
        batch_size = 10000
        updated_count = 0
        processed_count = 0

        print("从案号中提取年份信息...")

        # 使用游标分批处理
        cursor = db['documents'].find(query, {'案号': 1}).batch_size(batch_size)

        batch_updates = []

        for doc in cursor:
            case_number = doc.get('案号', '')
            year = extract_year_from_case_number(case_number)

            if year:
                batch_updates.append({
                    '_id': doc['_id'],
                    'year': year
                })

            processed_count += 1

            # 当批次满了或处理完所有文档时，执行批量更新
            if len(batch_updates) >= batch_size or processed_count == total_docs:
                if batch_updates:
                    # 执行批量更新
                    from pymongo import UpdateOne

                    bulk_ops = []
                    for update in batch_updates:
                        bulk_ops.append(
                            UpdateOne(
                                {'_id': update['_id']},
                                {'$set': {'数据年份': update['year']}}
                            )
                        )

                    if bulk_ops:
                        result = db['documents'].bulk_write(bulk_ops)
                        updated_count += result.modified_count
                        print(f"已处理 {processed_count}/{total_docs} 条记录，更新了 {result.modified_count} 条")

                    batch_updates = []

        print(f"从案号提取年份完成，更新了 {updated_count} 条记录")

        # 尝试从文件名提取年月信息（作为补充）
        print("\n尝试从文件名提取年月信息...")
        file_sources = db['documents'].distinct('文件来源', query)
        print(f"发现 {len(file_sources)} 个不同的文件来源")

        for source in file_sources:
            if not source:
                continue

            year, month = extract_year_month_from_filename(source)

            if year:
                # 构建更新操作
                update_doc = {'数据年份': year}
                if month:
                    update_doc['数据月份'] = month

                # 更新所有来自这个文件的文档
                result = db['documents'].update_many(
                    {
                        '文件来源': source,
                        '$or': [
                            {'数据年份': {'$exists': False}},
                            {'数据年份': None}
                        ]
                    },
                    {'$set': update_doc}
                )

                updated_count += result.modified_count

                if month:
                    print(f"更新文件 {source}: {year}年{month}月, 更新了 {result.modified_count} 条记录")
                else:
                    print(f"更新文件 {source}: {year}年, 更新了 {result.modified_count} 条记录")
            else:
                print(f"无法从文件名提取年月信息: {source}")
        
        print(f"\n总共更新了 {updated_count} 条记录")
        
        # 验证更新结果
        remaining_docs = db['documents'].count_documents(query)
        print(f"剩余未更新的文档数: {remaining_docs}")
        
        # 统计年月分布
        print("\n年月分布统计:")
        pipeline = [
            {'$match': {'数据年份': {'$exists': True, '$ne': None}}},
            {'$group': {
                '_id': {
                    'year': '$数据年份',
                    'month': '$数据月份'
                },
                'count': {'$sum': 1}
            }},
            {'$sort': {'_id.year': 1, '_id.month': 1}}
        ]
        
        year_month_stats = list(db['documents'].aggregate(pipeline))
        
        for item in year_month_stats[:10]:  # 显示前10个
            year = item['_id']['year']
            month = item['_id'].get('month')
            count = item['count']

            if month:
                print(f"  {year}年{month:02d}月: {count} 条记录")
            else:
                print(f"  {year}年: {count} 条记录")
        
        if len(year_month_stats) > 10:
            print(f"  ... 还有 {len(year_month_stats) - 10} 个时间段")
        
        client.close()
        return True
        
    except Exception as e:
        print(f"添加年月信息失败: {e}")
        return False

def main():
    """主函数"""
    print("="*60)
    print("为现有数据添加年月信息")
    print("="*60)
    
    success = add_year_month_to_existing_data()
    
    if success:
        print("\n年月信息添加完成！")
    else:
        print("\n年月信息添加失败！")

if __name__ == "__main__":
    main()
