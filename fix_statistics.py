#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复统计信息脚本
重新计算和更新数据库中的统计信息
"""

from pymongo import MongoClient
from datetime import datetime
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_statistics():
    """修复统计信息"""
    try:
        client = MongoClient('mongodb://localhost:27017/')
        db = client['judicial_documents']
        
        print("开始修复统计信息...")
        
        # 1. 计算总文档数
        total_docs = db['documents'].count_documents({})
        print(f"总文档数: {total_docs}")
        
        # 2. 计算地区分布
        print("计算地区分布...")
        region_pipeline = [
            {'$group': {'_id': '$所属地区', 'count': {'$sum': 1}}},
            {'$sort': {'count': -1}}
        ]
        region_stats = {}
        for item in db['documents'].aggregate(region_pipeline):
            if item['_id']:  # 排除空值
                region_stats[item['_id']] = item['count']
        
        print(f"地区分布统计完成，共 {len(region_stats)} 个地区")
        
        # 3. 计算案件类型分布
        print("计算案件类型分布...")
        case_type_pipeline = [
            {'$group': {'_id': '$案件类型', 'count': {'$sum': 1}}},
            {'$sort': {'count': -1}}
        ]
        case_type_stats = {}
        for item in db['documents'].aggregate(case_type_pipeline):
            if item['_id']:  # 排除空值
                case_type_stats[item['_id']] = item['count']
        
        print(f"案件类型分布统计完成，共 {len(case_type_stats)} 种类型")
        
        # 4. 更新统计信息到数据库
        current_time = datetime.now()
        
        # 更新总文档数
        db['statistics'].update_one(
            {'stat_type': 'total_documents'},
            {
                '$set': {
                    'stat_type': 'total_documents',
                    'value': total_docs,
                    'description': '文书总数',
                    'update_time': current_time
                }
            },
            upsert=True
        )
        
        # 更新导入进度统计
        db['statistics'].update_one(
            {'stat_type': 'import_progress'},
            {
                '$set': {
                    'stat_type': 'import_progress',
                    'value': {
                        'total_files': 0,
                        'processed_files': 0,
                        'failed_files': 0,
                        'success_rate': 100.0,
                        'total_records': total_docs,
                        'imported_records': total_docs,
                        'duplicate_records': 0,
                        'error_records': 0
                    },
                    'description': '导入进度统计',
                    'update_time': current_time
                }
            },
            upsert=True
        )
        
        # 更新地区分布统计
        db['statistics'].update_one(
            {'stat_type': 'region_distribution'},
            {
                '$set': {
                    'stat_type': 'region_distribution',
                    'value': region_stats,
                    'description': '地区分布统计',
                    'update_time': current_time
                }
            },
            upsert=True
        )
        
        # 更新案件类型分布统计
        db['statistics'].update_one(
            {'stat_type': 'case_type_distribution'},
            {
                '$set': {
                    'stat_type': 'case_type_distribution',
                    'value': case_type_stats,
                    'description': '案件类型分布统计',
                    'update_time': current_time
                }
            },
            upsert=True
        )
        
        print("统计信息修复完成！")
        
        # 显示修复后的统计信息
        print("\n修复后的统计信息:")
        for stat in db['statistics'].find():
            print(f"- {stat['stat_type']}: {stat['description']}")
            if stat['stat_type'] == 'total_documents':
                print(f"  值: {stat['value']}")
            elif stat['stat_type'] == 'import_progress':
                progress = stat['value']
                print(f"  总记录数: {progress['total_records']}")
                print(f"  导入记录数: {progress['imported_records']}")
            elif stat['stat_type'] == 'region_distribution':
                top_regions = sorted(stat['value'].items(), key=lambda x: x[1], reverse=True)[:5]
                print(f"  前5个地区: {top_regions}")
            elif stat['stat_type'] == 'case_type_distribution':
                top_types = sorted(stat['value'].items(), key=lambda x: x[1], reverse=True)[:5]
                print(f"  前5个案件类型: {top_types}")
        
        client.close()
        return True
        
    except Exception as e:
        print(f"修复统计信息失败: {e}")
        return False

if __name__ == "__main__":
    fix_statistics()
