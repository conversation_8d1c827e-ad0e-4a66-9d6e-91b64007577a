#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导入剩余数据脚本
专门用于导入所有剩余的裁判文书数据
"""

import os
import sys
import zipfile
import pandas as pd
import json
import ast
import re
import shutil
from datetime import datetime
from pathlib import Path
from pymongo import MongoClient
from pymongo.errors import DuplicateKeyError, BulkWriteError
import logging
from tqdm import tqdm

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RemainingDataImporter:
    def __init__(self, connection_string="mongodb://localhost:27017/", db_name="judicial_documents"):
        """
        初始化剩余数据导入器
        """
        self.connection_string = connection_string
        self.db_name = db_name
        self.client = None
        self.db = None
        
        # 统计信息
        self.stats = {
            'total_files': 0,
            'processed_files': 0,
            'failed_files': 0,
            'total_records': 0,
            'imported_records': 0,
            'duplicate_records': 0,
            'error_records': 0
        }
    
    def connect(self):
        """连接到MongoDB"""
        try:
            self.client = MongoClient(self.connection_string)
            self.db = self.client[self.db_name]
            # 测试连接
            self.client.admin.command('ping')
            logger.info(f"成功连接到MongoDB数据库: {self.db_name}")
            return True
        except Exception as e:
            logger.error(f"连接MongoDB失败: {e}")
            return False
    
    def extract_year_month_from_filename(self, filename):
        """从文件名中提取年月信息"""
        if not filename:
            return None, None
        
        # 尝试多种模式匹配年月
        patterns = [
            r'(\d{4})年(\d{1,2})月',  # 2021年01月
            r'(\d{4})年?(\d{1,2})',   # 2021年1 或 20211
            r'(\d{4})-(\d{1,2})',     # 2021-01
            r'(\d{4})_(\d{1,2})',     # 2021_01
            r'(\d{4})\.(\d{1,2})',    # 2021.01
            r'(\d{4})(\d{2})',        # 202101
        ]
        
        for pattern in patterns:
            match = re.search(pattern, filename)
            if match:
                year = int(match.group(1))
                month = int(match.group(2))
                if 2000 <= year <= 2030 and 1 <= month <= 12:
                    return year, month
        
        # 如果没有找到月份，尝试只匹配年份
        year_match = re.search(r'(\d{4})', filename)
        if year_match:
            year = int(year_match.group(1))
            if 2000 <= year <= 2030:
                return year, None
        
        return None, None
    
    def extract_year_from_case_number(self, case_number):
        """从案号中提取年份信息"""
        if not case_number:
            return None
        
        # 案号格式通常是：（2021）京01民终8315号
        match = re.search(r'[（(](\d{4})[）)]', case_number)
        if match:
            year = int(match.group(1))
            if 2000 <= year <= 2030:
                return year
        
        return None
    
    def clean_data(self, df, source_file=""):
        """清洗数据并添加年月信息"""
        try:
            # 处理案由字段
            if '案由' in df.columns:
                def parse_case_cause(cause_str):
                    if pd.isna(cause_str) or cause_str == '':
                        return []
                    try:
                        if isinstance(cause_str, str) and cause_str.startswith('['):
                            return ast.literal_eval(cause_str)
                        else:
                            return [str(cause_str)]
                    except:
                        return [str(cause_str)]
                
                df['案由'] = df['案由'].apply(parse_case_cause)
            
            # 处理裁判日期
            if '裁判日期' in df.columns:
                def parse_date(date_str):
                    if pd.isna(date_str):
                        return None
                    return str(date_str) if date_str != '' else None
                
                df['裁判日期'] = df['裁判日期'].apply(parse_date)
            
            # 从文件名提取年月信息
            year, month = self.extract_year_month_from_filename(source_file)
            
            # 如果文件名没有年月信息，尝试从案号提取年份
            if not year and '案号' in df.columns:
                def extract_year_from_row(row):
                    case_year = self.extract_year_from_case_number(row.get('案号', ''))
                    return case_year if case_year else year
                
                df['数据年份'] = df.apply(extract_year_from_row, axis=1)
            else:
                if year:
                    df['数据年份'] = year
            
            if month:
                df['数据月份'] = month
            
            # 添加导入时间和文件来源
            df['导入时间'] = datetime.now()
            df['文件来源'] = os.path.basename(source_file)
            
            # 填充空值
            df = df.fillna('')
            
            # 确保必需字段不为空
            required_fields = ['案号', '所属地区', '案件类型', '审理程序']
            original_count = len(df)
            for field in required_fields:
                if field in df.columns:
                    df = df[df[field] != '']
            
            if len(df) < original_count:
                logger.info(f"数据清洗：从 {original_count} 条记录过滤到 {len(df)} 条记录")
            
            return df
            
        except Exception as e:
            logger.error(f"数据清洗失败: {e}")
            return df
    
    def bulk_insert_records(self, records, batch_size=1000):
        """批量插入记录"""
        imported = 0
        duplicates = 0
        errors = 0
        
        try:
            # 分批插入
            for i in range(0, len(records), batch_size):
                batch = records[i:i + batch_size]
                
                try:
                    result = self.db['documents'].insert_many(batch, ordered=False)
                    imported += len(result.inserted_ids)
                    
                except BulkWriteError as bwe:
                    # 处理批量写入错误
                    for error in bwe.details['writeErrors']:
                        if error['code'] == 11000:  # 重复键错误
                            duplicates += 1
                        else:
                            errors += 1
                    
                    # 计算成功插入的数量
                    imported += bwe.details['nInserted']
                
                except Exception as e:
                    logger.error(f"批量插入失败: {e}")
                    errors += len(batch)
        
        except Exception as e:
            logger.error(f"批量插入过程失败: {e}")
            errors = len(records)
        
        return imported, duplicates, errors
    
    def process_zip_file(self, zip_path):
        """处理单个ZIP文件"""
        try:
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                file_list = zip_ref.namelist()
                
                # 查找CSV文件
                csv_files = [f for f in file_list if f.endswith('.csv') and not f.startswith('__MACOSX')]
                
                total_imported = 0
                total_duplicates = 0
                total_errors = 0
                
                for csv_file in csv_files:
                    try:
                        # 读取CSV文件
                        with zip_ref.open(csv_file) as csv_data:
                            # 尝试不同的编码
                            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
                            df = None
                            
                            for encoding in encodings:
                                try:
                                    csv_data.seek(0)
                                    df = pd.read_csv(csv_data, encoding=encoding)
                                    break
                                except Exception:
                                    continue
                            
                            if df is None:
                                logger.error(f"无法读取CSV文件: {csv_file}")
                                continue
                            
                            # 数据清洗
                            df = self.clean_data(df, zip_path)
                            
                            if df.empty:
                                logger.warning(f"CSV文件清洗后为空: {csv_file}")
                                continue
                            
                            # 转换为字典列表并插入
                            records = df.to_dict('records')
                            imported, duplicates, errors = self.bulk_insert_records(records)
                            
                            total_imported += imported
                            total_duplicates += duplicates
                            total_errors += errors
                            
                            logger.info(f"处理 {csv_file}: 导入 {imported}, 重复 {duplicates}, 错误 {errors}")
                    
                    except Exception as e:
                        logger.error(f"处理CSV文件失败 {csv_file}: {e}")
                        total_errors += 1
                
                return total_imported, total_duplicates, total_errors
        
        except Exception as e:
            logger.error(f"处理ZIP文件失败 {zip_path}: {e}")
            return 0, 0, 1
    
    def import_all_remaining_data(self, data_dir="裁判文书"):
        """导入所有剩余数据"""
        if not self.connect():
            return False
        
        try:
            print(f"开始导入 {data_dir} 目录下的所有数据...")
            
            # 收集所有ZIP文件
            zip_files = []
            for root, dirs, files in os.walk(data_dir):
                for file in files:
                    if file.endswith('.zip'):
                        zip_files.append(os.path.join(root, file))
            
            print(f"找到 {len(zip_files)} 个ZIP文件")
            self.stats['total_files'] = len(zip_files)
            
            if not zip_files:
                print("没有找到ZIP文件")
                return True
            
            # 处理每个ZIP文件
            with tqdm(total=len(zip_files), desc="处理ZIP文件") as pbar:
                for zip_file in zip_files:
                    try:
                        imported, duplicates, errors = self.process_zip_file(zip_file)
                        
                        # 更新统计
                        self.stats['processed_files'] += 1
                        self.stats['imported_records'] += imported
                        self.stats['duplicate_records'] += duplicates
                        self.stats['error_records'] += errors
                        
                        logger.info(f"完成 {zip_file}: 导入 {imported}, 重复 {duplicates}, 错误 {errors}")
                        
                        # 更新进度到数据库
                        self.update_progress(os.path.basename(zip_file), 'completed', 
                                           processed_records=imported + duplicates)
                        
                    except Exception as e:
                        logger.error(f"处理ZIP文件失败 {zip_file}: {e}")
                        self.stats['failed_files'] += 1
                        self.update_progress(os.path.basename(zip_file), 'failed', 
                                           error_message=str(e))
                    
                    pbar.update(1)
            
            # 更新最终统计信息
            self.update_final_statistics()
            
            # 打印统计信息
            self.print_final_stats()
            
            return True
            
        except Exception as e:
            logger.error(f"导入过程中发生错误: {e}")
            return False
        finally:
            if self.client:
                self.client.close()
    
    def update_progress(self, file_name, status, **kwargs):
        """更新导入进度"""
        try:
            progress_data = {
                'file_name': file_name,
                'status': status,
                'update_time': datetime.now()
            }
            progress_data.update(kwargs)
            
            self.db['import_progress'].update_one(
                {'file_name': file_name},
                {'$set': progress_data},
                upsert=True
            )
        except Exception as e:
            logger.error(f"更新进度失败: {e}")
    
    def update_final_statistics(self):
        """更新最终统计信息"""
        try:
            # 重新计算总文档数
            total_docs = self.db['documents'].count_documents({})
            
            # 更新统计信息
            current_time = datetime.now()
            
            self.db['statistics'].update_one(
                {'stat_type': 'total_documents'},
                {'$set': {'value': total_docs, 'update_time': current_time}},
                upsert=True
            )
            
            # 更新导入进度统计
            import_stats = {
                'total_files': self.stats['total_files'],
                'processed_files': self.stats['processed_files'],
                'failed_files': self.stats['failed_files'],
                'success_rate': (self.stats['processed_files'] / self.stats['total_files'] * 100) if self.stats['total_files'] > 0 else 0,
                'total_records': self.stats['imported_records'] + self.stats['duplicate_records'],
                'imported_records': self.stats['imported_records'],
                'duplicate_records': self.stats['duplicate_records'],
                'error_records': self.stats['error_records']
            }
            
            self.db['statistics'].update_one(
                {'stat_type': 'import_progress'},
                {'$set': {'value': import_stats, 'update_time': current_time}},
                upsert=True
            )
            
            logger.info("最终统计信息已更新")
            
        except Exception as e:
            logger.error(f"更新统计信息失败: {e}")
    
    def print_final_stats(self):
        """打印最终统计信息"""
        print("\n" + "="*60)
        print("剩余数据导入完成统计")
        print("="*60)
        print(f"总ZIP文件数: {self.stats['total_files']}")
        print(f"成功处理: {self.stats['processed_files']}")
        print(f"处理失败: {self.stats['failed_files']}")
        print(f"成功率: {(self.stats['processed_files'] / self.stats['total_files'] * 100):.2f}%" if self.stats['total_files'] > 0 else "0%")
        print(f"导入记录: {self.stats['imported_records']}")
        print(f"重复记录: {self.stats['duplicate_records']}")
        print(f"错误记录: {self.stats['error_records']}")
        print(f"总处理记录: {self.stats['imported_records'] + self.stats['duplicate_records'] + self.stats['error_records']}")
        print("="*60)

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='裁判文书剩余数据导入工具')
    parser.add_argument('--data-dir', default='裁判文书', help='数据目录路径')
    parser.add_argument('--connection', default='mongodb://localhost:27017/', help='MongoDB连接字符串')
    parser.add_argument('--database', default='judicial_documents', help='数据库名称')
    
    args = parser.parse_args()
    
    importer = RemainingDataImporter(args.connection, args.database)
    
    print(f"开始导入剩余数据...")
    print(f"数据目录: {args.data_dir}")
    print(f"数据库: {args.database}")
    
    success = importer.import_all_remaining_data(args.data_dir)
    
    if success:
        print("剩余数据导入完成！")
    else:
        print("剩余数据导入失败！")

if __name__ == "__main__":
    main()
