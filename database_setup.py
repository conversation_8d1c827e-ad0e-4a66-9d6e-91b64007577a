#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MongoDB数据库设置脚本
用于创建数据库、集合和索引
"""

import os
from pymongo import MongoClient, ASCENDING, TEXT
from pymongo.errors import ConnectionFailure, CollectionInvalid
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DatabaseSetup:
    def __init__(self, connection_string="mongodb://localhost:27017/", db_name="judicial_documents"):
        """
        初始化数据库连接
        
        Args:
            connection_string: MongoDB连接字符串
            db_name: 数据库名称
        """
        self.connection_string = connection_string
        self.db_name = db_name
        self.client = None
        self.db = None
        
    def connect(self):
        """连接到MongoDB"""
        try:
            self.client = MongoClient(self.connection_string, serverSelectionTimeoutMS=5000)
            # 测试连接
            self.client.admin.command('ping')
            self.db = self.client[self.db_name]
            logger.info(f"成功连接到MongoDB数据库: {self.db_name}")
            return True
        except ConnectionFailure as e:
            logger.error(f"连接MongoDB失败: {e}")
            return False
        except Exception as e:
            logger.error(f"连接时发生未知错误: {e}")
            return False
    
    def create_collections(self):
        """创建集合"""
        collections_to_create = [
            {
                'name': 'documents',
                'description': '裁判文书主集合',
                'validator': {
                    '$jsonSchema': {
                        'bsonType': 'object',
                        'required': ['案号', '所属地区', '案件类型', '审理程序'],
                        'properties': {
                            '案号': {'bsonType': 'string'},
                            '所属地区': {'bsonType': 'string'},
                            '案件类型': {'bsonType': 'string'},
                            '审理程序': {'bsonType': 'string'},
                            '案件名称': {'bsonType': 'string'},
                            '案由': {'bsonType': 'array'},
                            '全文': {'bsonType': 'string'},
                            '裁判日期': {'bsonType': ['string', 'null']},
                            '数据年份': {'bsonType': ['int', 'null']},
                            '数据月份': {'bsonType': ['int', 'null']},
                            '导入时间': {'bsonType': 'date'},
                            '文件来源': {'bsonType': 'string'}
                        }
                    }
                }
            },
            {
                'name': 'import_progress',
                'description': '导入进度跟踪集合',
                'validator': {
                    '$jsonSchema': {
                        'bsonType': 'object',
                        'required': ['file_name', 'status', 'start_time'],
                        'properties': {
                            'file_name': {'bsonType': 'string'},
                            'status': {'enum': ['pending', 'processing', 'completed', 'failed']},
                            'start_time': {'bsonType': 'date'},
                            'end_time': {'bsonType': ['date', 'null']},
                            'total_records': {'bsonType': ['int', 'null']},
                            'processed_records': {'bsonType': 'int'},
                            'error_message': {'bsonType': ['string', 'null']}
                        }
                    }
                }
            },
            {
                'name': 'statistics',
                'description': '统计信息集合',
                'validator': {
                    '$jsonSchema': {
                        'bsonType': 'object',
                        'required': ['stat_type', 'value', 'update_time'],
                        'properties': {
                            'stat_type': {'bsonType': 'string'},
                            'value': {'bsonType': ['int', 'double', 'object']},
                            'update_time': {'bsonType': 'date'},
                            'description': {'bsonType': 'string'}
                        }
                    }
                }
            }
        ]
        
        for collection_info in collections_to_create:
            try:
                # 检查集合是否已存在
                if collection_info['name'] in self.db.list_collection_names():
                    logger.info(f"集合 '{collection_info['name']}' 已存在")
                    continue
                
                # 创建集合
                self.db.create_collection(
                    collection_info['name'],
                    validator=collection_info.get('validator')
                )
                logger.info(f"成功创建集合: {collection_info['name']} - {collection_info['description']}")
                
            except CollectionInvalid as e:
                logger.warning(f"集合 '{collection_info['name']}' 创建失败: {e}")
            except Exception as e:
                logger.error(f"创建集合 '{collection_info['name']}' 时发生错误: {e}")
    
    def create_indexes(self):
        """创建索引"""
        indexes_config = [
            {
                'collection': 'documents',
                'indexes': [
                    {'fields': [('案号', ASCENDING)], 'unique': True, 'name': 'idx_case_number'},
                    {'fields': [('所属地区', ASCENDING)], 'name': 'idx_region'},
                    {'fields': [('案件类型', ASCENDING)], 'name': 'idx_case_type'},
                    {'fields': [('审理程序', ASCENDING)], 'name': 'idx_procedure'},
                    {'fields': [('裁判日期', ASCENDING)], 'name': 'idx_judgment_date'},
                    {'fields': [('导入时间', ASCENDING)], 'name': 'idx_import_time'},
                    {'fields': [('案由', ASCENDING)], 'name': 'idx_case_cause'},
                    {'fields': [('全文', TEXT)], 'name': 'idx_fulltext_search'},
                    {'fields': [('数据年份', ASCENDING)], 'name': 'idx_data_year'},
                    {'fields': [('数据月份', ASCENDING)], 'name': 'idx_data_month'},
                    {'fields': [('数据年份', ASCENDING), ('数据月份', ASCENDING)], 'name': 'idx_year_month'},
                    {'fields': [('所属地区', ASCENDING), ('案件类型', ASCENDING)], 'name': 'idx_region_type'},
                    {'fields': [('裁判日期', ASCENDING), ('案件类型', ASCENDING)], 'name': 'idx_date_type'},
                    {'fields': [('数据年份', ASCENDING), ('案件类型', ASCENDING)], 'name': 'idx_year_type'}
                ]
            },
            {
                'collection': 'import_progress',
                'indexes': [
                    {'fields': [('file_name', ASCENDING)], 'unique': True, 'name': 'idx_filename'},
                    {'fields': [('status', ASCENDING)], 'name': 'idx_status'},
                    {'fields': [('start_time', ASCENDING)], 'name': 'idx_start_time'}
                ]
            },
            {
                'collection': 'statistics',
                'indexes': [
                    {'fields': [('stat_type', ASCENDING)], 'name': 'idx_stat_type'},
                    {'fields': [('update_time', ASCENDING)], 'name': 'idx_update_time'}
                ]
            }
        ]
        
        for config in indexes_config:
            collection = self.db[config['collection']]
            
            for index_info in config['indexes']:
                try:
                    # 检查索引是否已存在
                    existing_indexes = collection.list_indexes()
                    index_names = [idx['name'] for idx in existing_indexes]
                    
                    if index_info['name'] in index_names:
                        logger.info(f"索引 '{index_info['name']}' 已存在于集合 '{config['collection']}'")
                        continue
                    
                    # 创建索引
                    index_options = {k: v for k, v in index_info.items() if k != 'fields'}
                    collection.create_index(index_info['fields'], **index_options)
                    logger.info(f"成功创建索引: {index_info['name']} 于集合 '{config['collection']}'")
                    
                except Exception as e:
                    logger.error(f"创建索引 '{index_info['name']}' 失败: {e}")
    
    def initialize_statistics(self):
        """初始化统计信息"""
        from datetime import datetime
        
        stats_collection = self.db['statistics']
        
        initial_stats = [
            {
                'stat_type': 'total_documents',
                'value': 0,
                'description': '文书总数',
                'update_time': datetime.now()
            },
            {
                'stat_type': 'import_progress',
                'value': {
                    'total_files': 0,
                    'processed_files': 0,
                    'failed_files': 0,
                    'success_rate': 0.0
                },
                'description': '导入进度统计',
                'update_time': datetime.now()
            },
            {
                'stat_type': 'region_distribution',
                'value': {},
                'description': '地区分布统计',
                'update_time': datetime.now()
            },
            {
                'stat_type': 'case_type_distribution',
                'value': {},
                'description': '案件类型分布统计',
                'update_time': datetime.now()
            }
        ]
        
        for stat in initial_stats:
            try:
                # 使用upsert确保不重复插入
                stats_collection.update_one(
                    {'stat_type': stat['stat_type']},
                    {'$setOnInsert': stat},
                    upsert=True
                )
                logger.info(f"初始化统计信息: {stat['stat_type']}")
            except Exception as e:
                logger.error(f"初始化统计信息失败 '{stat['stat_type']}': {e}")
    
    def setup_database(self):
        """完整的数据库设置流程"""
        logger.info("开始设置数据库...")
        
        if not self.connect():
            return False
        
        try:
            # 创建集合
            self.create_collections()
            
            # 创建索引
            self.create_indexes()
            
            # 初始化统计信息
            self.initialize_statistics()
            
            logger.info("数据库设置完成！")
            return True
            
        except Exception as e:
            logger.error(f"数据库设置过程中发生错误: {e}")
            return False
        finally:
            if self.client:
                self.client.close()
    
    def get_database_info(self):
        """获取数据库信息"""
        if not self.connect():
            return None
        
        try:
            info = {
                'database_name': self.db_name,
                'collections': [],
                'total_size': 0
            }
            
            # 获取集合信息
            for collection_name in self.db.list_collection_names():
                collection = self.db[collection_name]
                stats = self.db.command("collStats", collection_name)
                
                collection_info = {
                    'name': collection_name,
                    'count': collection.count_documents({}),
                    'size': stats.get('size', 0),
                    'indexes': len(list(collection.list_indexes()))
                }
                info['collections'].append(collection_info)
                info['total_size'] += collection_info['size']
            
            return info
            
        except Exception as e:
            logger.error(f"获取数据库信息失败: {e}")
            return None
        finally:
            if self.client:
                self.client.close()

def main():
    """主函数"""
    # 可以通过环境变量配置连接字符串
    connection_string = os.getenv('MONGODB_CONNECTION_STRING', 'mongodb://localhost:27017/')
    db_name = os.getenv('MONGODB_DATABASE_NAME', 'judicial_documents')
    
    db_setup = DatabaseSetup(connection_string, db_name)
    
    if db_setup.setup_database():
        print("数据库设置成功！")
        
        # 显示数据库信息
        info = db_setup.get_database_info()
        if info:
            print(f"\n数据库信息:")
            print(f"数据库名称: {info['database_name']}")
            print(f"总大小: {info['total_size']} 字节")
            print(f"集合数量: {len(info['collections'])}")
            
            for collection in info['collections']:
                print(f"  - {collection['name']}: {collection['count']} 文档, {collection['indexes']} 个索引")
    else:
        print("数据库设置失败！")

if __name__ == "__main__":
    main()
