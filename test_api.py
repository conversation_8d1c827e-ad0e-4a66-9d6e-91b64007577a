#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试API接口脚本
"""

import requests
import json

def test_api_endpoints():
    """测试所有API端点"""
    base_url = "http://localhost:5000"
    
    endpoints = [
        "/api/statistics",
        "/api/progress", 
        "/api/documents/count",
        "/api/statistics/region",
        "/api/statistics/case_type",
        "/api/statistics/year_month",
        "/api/statistics/yearly",
        "/health"
    ]
    
    print("测试API端点...")
    print("="*50)
    
    for endpoint in endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=10)
            print(f"✓ {endpoint}: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                
                if endpoint == "/api/statistics":
                    print(f"  总文档数: {data.get('total_documents', 0)}")
                    if 'import_progress' in data:
                        progress = data['import_progress']
                        print(f"  导入记录数: {progress.get('imported_records', 0)}")
                        print(f"  成功率: {progress.get('success_rate', 0):.2f}%")
                
                elif endpoint == "/api/documents/count":
                    print(f"  文档数量: {data.get('count', 0)}")
                
                elif endpoint == "/api/statistics/region":
                    if isinstance(data, list) and len(data) > 0:
                        print(f"  前3个地区: {data[:3]}")
                
                elif endpoint == "/api/statistics/case_type":
                    if isinstance(data, list) and len(data) > 0:
                        print(f"  前3个案件类型: {data[:3]}")
                
                elif endpoint == "/api/statistics/yearly":
                    if isinstance(data, list) and len(data) > 0:
                        print(f"  年度统计: {data}")
                
                elif endpoint == "/api/statistics/year_month":
                    if isinstance(data, list) and len(data) > 0:
                        print(f"  年月统计（前5个）: {data[:5]}")
                
                elif endpoint == "/health":
                    print(f"  状态: {data.get('status', 'unknown')}")
            
            print()
                    
        except Exception as e:
            print(f"✗ {endpoint}: {e}")
            print()

if __name__ == "__main__":
    test_api_endpoints()
