#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
裁判文书数据导入脚本
用于批量处理和导入所有裁判文书数据到MongoDB
"""

import os
import zipfile
import pandas as pd
import json
import ast
from datetime import datetime
from pathlib import Path
from pymongo import MongoClient
from pymongo.errors import DuplicateKeyError, BulkWriteError
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from tqdm import tqdm

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DataImporter:
    def __init__(self, connection_string="mongodb://localhost:27017/", db_name="judicial_documents"):
        """
        初始化数据导入器
        
        Args:
            connection_string: MongoDB连接字符串
            db_name: 数据库名称
        """
        self.connection_string = connection_string
        self.db_name = db_name
        self.client = None
        self.db = None
        self.lock = threading.Lock()
        
        # 统计信息
        self.stats = {
            'total_files': 0,
            'processed_files': 0,
            'failed_files': 0,
            'total_records': 0,
            'imported_records': 0,
            'duplicate_records': 0,
            'error_records': 0
        }
    
    def connect(self):
        """连接到MongoDB"""
        try:
            self.client = MongoClient(self.connection_string)
            self.db = self.client[self.db_name]
            # 测试连接
            self.client.admin.command('ping')
            logger.info(f"成功连接到MongoDB数据库: {self.db_name}")
            return True
        except Exception as e:
            logger.error(f"连接MongoDB失败: {e}")
            return False
    
    def update_progress(self, file_name, status, **kwargs):
        """更新导入进度"""
        try:
            progress_data = {
                'file_name': file_name,
                'status': status,
                'update_time': datetime.now()
            }
            progress_data.update(kwargs)
            
            self.db['import_progress'].update_one(
                {'file_name': file_name},
                {'$set': progress_data},
                upsert=True
            )
        except Exception as e:
            logger.error(f"更新进度失败: {e}")
    
    def clean_data(self, df):
        """清洗数据"""
        try:
            # 处理案由字段（从字符串转换为列表）
            if '案由' in df.columns:
                def parse_case_cause(cause_str):
                    if pd.isna(cause_str) or cause_str == '':
                        return []
                    try:
                        # 尝试解析为Python列表
                        if isinstance(cause_str, str) and cause_str.startswith('['):
                            return ast.literal_eval(cause_str)
                        else:
                            return [str(cause_str)]
                    except:
                        return [str(cause_str)]
                
                df['案由'] = df['案由'].apply(parse_case_cause)
            
            # 处理裁判日期
            if '裁判日期' in df.columns:
                def parse_date(date_str):
                    if pd.isna(date_str):
                        return None
                    return str(date_str) if date_str != '' else None
                
                df['裁判日期'] = df['裁判日期'].apply(parse_date)
            
            # 填充空值
            df = df.fillna('')
            
            # 确保必需字段不为空
            required_fields = ['案号', '所属地区', '案件类型', '审理程序']
            for field in required_fields:
                if field in df.columns:
                    df = df[df[field] != '']
            
            return df
            
        except Exception as e:
            logger.error(f"数据清洗失败: {e}")
            return df
    
    def process_csv_file(self, csv_file_path, source_file):
        """处理单个CSV文件"""
        try:
            # 尝试不同的编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
            df = None
            
            for encoding in encodings:
                try:
                    df = pd.read_csv(csv_file_path, encoding=encoding)
                    logger.info(f"成功读取CSV文件 {csv_file_path}，使用编码: {encoding}")
                    break
                except Exception as e:
                    continue
            
            if df is None:
                raise Exception("无法读取CSV文件，所有编码都失败")
            
            # 数据清洗
            df = self.clean_data(df)
            
            if df.empty:
                logger.warning(f"CSV文件 {csv_file_path} 清洗后为空")
                return 0, 0, 0
            
            # 添加元数据
            df['导入时间'] = datetime.now()
            df['文件来源'] = source_file
            
            # 转换为字典列表
            records = df.to_dict('records')
            
            # 批量插入
            return self.bulk_insert_records(records)
            
        except Exception as e:
            logger.error(f"处理CSV文件 {csv_file_path} 失败: {e}")
            return 0, 0, len(df) if df is not None else 0
    
    def bulk_insert_records(self, records, batch_size=1000):
        """批量插入记录"""
        imported = 0
        duplicates = 0
        errors = 0
        
        try:
            # 分批插入
            for i in range(0, len(records), batch_size):
                batch = records[i:i + batch_size]
                
                try:
                    result = self.db['documents'].insert_many(batch, ordered=False)
                    imported += len(result.inserted_ids)
                    
                except BulkWriteError as bwe:
                    # 处理批量写入错误
                    for error in bwe.details['writeErrors']:
                        if error['code'] == 11000:  # 重复键错误
                            duplicates += 1
                        else:
                            errors += 1
                            logger.error(f"插入错误: {error}")
                    
                    # 计算成功插入的数量
                    imported += bwe.details['nInserted']
                
                except Exception as e:
                    logger.error(f"批量插入失败: {e}")
                    errors += len(batch)
        
        except Exception as e:
            logger.error(f"批量插入过程失败: {e}")
            errors = len(records)
        
        return imported, duplicates, errors
    
    def extract_and_process_zip(self, zip_path, max_depth=3, current_depth=0):
        """递归解压并处理ZIP文件"""
        if current_depth >= max_depth:
            logger.warning(f"达到最大解压深度，跳过: {zip_path}")
            return 0, 0, 0
        
        imported = 0
        duplicates = 0
        errors = 0
        
        try:
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                file_list = zip_ref.namelist()
                
                # 查找CSV文件
                csv_files = [f for f in file_list if f.endswith('.csv') and not f.startswith('__MACOSX')]
                
                for csv_file in csv_files:
                    try:
                        # 提取CSV文件到临时位置
                        temp_csv_path = f"temp_csv_{threading.current_thread().ident}_{current_depth}.csv"
                        
                        with zip_ref.open(csv_file) as source:
                            with open(temp_csv_path, 'wb') as target:
                                target.write(source.read())
                        
                        # 处理CSV文件
                        i, d, e = self.process_csv_file(temp_csv_path, os.path.basename(zip_path))
                        imported += i
                        duplicates += d
                        errors += e
                        
                        # 清理临时文件
                        if os.path.exists(temp_csv_path):
                            os.remove(temp_csv_path)
                            
                    except Exception as e:
                        logger.error(f"处理CSV文件 {csv_file} 失败: {e}")
                        errors += 1
                
                # 如果没有CSV文件，查找嵌套的ZIP文件
                if not csv_files:
                    nested_zips = [f for f in file_list if f.endswith('.zip') and not f.startswith('__MACOSX')]
                    
                    for nested_zip in nested_zips:
                        try:
                            # 提取嵌套ZIP文件
                            temp_zip_path = f"temp_zip_{threading.current_thread().ident}_{current_depth}.zip"
                            
                            with zip_ref.open(nested_zip) as source:
                                with open(temp_zip_path, 'wb') as target:
                                    target.write(source.read())
                            
                            # 递归处理
                            i, d, e = self.extract_and_process_zip(temp_zip_path, max_depth, current_depth + 1)
                            imported += i
                            duplicates += d
                            errors += e
                            
                            # 清理临时文件
                            if os.path.exists(temp_zip_path):
                                os.remove(temp_zip_path)
                                
                        except Exception as e:
                            logger.error(f"处理嵌套ZIP文件 {nested_zip} 失败: {e}")
                            errors += 1
        
        except Exception as e:
            logger.error(f"解压ZIP文件 {zip_path} 失败: {e}")
            errors += 1
        
        return imported, duplicates, errors
    
    def process_single_file(self, file_path):
        """处理单个文件"""
        file_name = os.path.basename(file_path)
        
        try:
            # 更新进度状态
            self.update_progress(file_name, 'processing', start_time=datetime.now())
            
            imported = 0
            duplicates = 0
            errors = 0
            
            if file_path.endswith('.zip'):
                imported, duplicates, errors = self.extract_and_process_zip(file_path)
            elif file_path.endswith('.csv'):
                imported, duplicates, errors = self.process_csv_file(file_path, file_name)
            else:
                logger.warning(f"不支持的文件类型: {file_path}")
                errors = 1
            
            # 更新统计信息
            with self.lock:
                self.stats['processed_files'] += 1
                self.stats['imported_records'] += imported
                self.stats['duplicate_records'] += duplicates
                self.stats['error_records'] += errors
            
            # 更新进度状态
            status = 'completed' if errors == 0 else 'failed'
            self.update_progress(
                file_name, 
                status,
                end_time=datetime.now(),
                processed_records=imported + duplicates,
                error_message=f"导入: {imported}, 重复: {duplicates}, 错误: {errors}" if errors > 0 else None
            )
            
            logger.info(f"文件 {file_name} 处理完成: 导入 {imported}, 重复 {duplicates}, 错误 {errors}")
            
        except Exception as e:
            logger.error(f"处理文件 {file_path} 失败: {e}")
            
            with self.lock:
                self.stats['failed_files'] += 1
                self.stats['error_records'] += 1
            
            self.update_progress(
                file_name,
                'failed',
                end_time=datetime.now(),
                error_message=str(e)
            )
    
    def import_all_data(self, data_dir="裁判文书", max_workers=4):
        """导入所有数据"""
        if not self.connect():
            return False
        
        try:
            # 收集所有需要处理的文件
            files_to_process = []
            
            for root, dirs, files in os.walk(data_dir):
                for file in files:
                    if file.endswith(('.zip', '.csv')):
                        files_to_process.append(os.path.join(root, file))
            
            self.stats['total_files'] = len(files_to_process)
            logger.info(f"找到 {len(files_to_process)} 个文件需要处理")
            
            if not files_to_process:
                logger.warning("没有找到需要处理的文件")
                return True
            
            # 使用线程池并行处理
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 提交所有任务
                future_to_file = {
                    executor.submit(self.process_single_file, file_path): file_path 
                    for file_path in files_to_process
                }
                
                # 使用tqdm显示进度
                with tqdm(total=len(files_to_process), desc="处理文件") as pbar:
                    for future in as_completed(future_to_file):
                        file_path = future_to_file[future]
                        try:
                            future.result()
                        except Exception as e:
                            logger.error(f"处理文件 {file_path} 时发生异常: {e}")
                        finally:
                            pbar.update(1)
            
            # 更新最终统计信息
            self.update_statistics()
            
            # 打印最终统计
            self.print_final_stats()
            
            return True
            
        except Exception as e:
            logger.error(f"导入数据过程中发生错误: {e}")
            return False
        finally:
            if self.client:
                self.client.close()
    
    def update_statistics(self):
        """更新数据库中的统计信息"""
        try:
            # 更新总文档数
            total_docs = self.db['documents'].count_documents({})
            self.db['statistics'].update_one(
                {'stat_type': 'total_documents'},
                {'$set': {'value': total_docs, 'update_time': datetime.now()}}
            )
            
            # 更新导入进度统计
            import_stats = {
                'total_files': self.stats['total_files'],
                'processed_files': self.stats['processed_files'],
                'failed_files': self.stats['failed_files'],
                'success_rate': (self.stats['processed_files'] / self.stats['total_files'] * 100) if self.stats['total_files'] > 0 else 0,
                'total_records': self.stats['imported_records'] + self.stats['duplicate_records'],
                'imported_records': self.stats['imported_records'],
                'duplicate_records': self.stats['duplicate_records'],
                'error_records': self.stats['error_records']
            }
            
            self.db['statistics'].update_one(
                {'stat_type': 'import_progress'},
                {'$set': {'value': import_stats, 'update_time': datetime.now()}}
            )
            
            logger.info("统计信息已更新")
            
        except Exception as e:
            logger.error(f"更新统计信息失败: {e}")
    
    def print_final_stats(self):
        """打印最终统计信息"""
        print("\n" + "="*50)
        print("数据导入完成统计")
        print("="*50)
        print(f"总文件数: {self.stats['total_files']}")
        print(f"成功处理: {self.stats['processed_files']}")
        print(f"处理失败: {self.stats['failed_files']}")
        print(f"成功率: {(self.stats['processed_files'] / self.stats['total_files'] * 100):.2f}%" if self.stats['total_files'] > 0 else "0%")
        print(f"导入记录: {self.stats['imported_records']}")
        print(f"重复记录: {self.stats['duplicate_records']}")
        print(f"错误记录: {self.stats['error_records']}")
        print(f"总处理记录: {self.stats['imported_records'] + self.stats['duplicate_records'] + self.stats['error_records']}")
        print("="*50)

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='裁判文书数据导入工具')
    parser.add_argument('--data-dir', default='裁判文书', help='数据目录路径')
    parser.add_argument('--workers', type=int, default=4, help='并行处理线程数')
    parser.add_argument('--connection', default='mongodb://localhost:27017/', help='MongoDB连接字符串')
    parser.add_argument('--database', default='judicial_documents', help='数据库名称')
    
    args = parser.parse_args()
    
    importer = DataImporter(args.connection, args.database)
    
    print(f"开始导入数据...")
    print(f"数据目录: {args.data_dir}")
    print(f"并行线程: {args.workers}")
    print(f"数据库: {args.database}")
    
    success = importer.import_all_data(args.data_dir, args.workers)
    
    if success:
        print("数据导入完成！")
    else:
        print("数据导入失败！")

if __name__ == "__main__":
    main()
