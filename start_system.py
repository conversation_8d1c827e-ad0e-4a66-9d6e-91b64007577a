#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统启动脚本
用于启动整个裁判文书导入和监控系统
"""

import os
import sys
import subprocess
import threading
import time
import argparse
from pathlib import Path

def check_mongodb():
    """检查MongoDB是否运行"""
    try:
        from pymongo import MongoClient
        client = MongoClient('mongodb://localhost:27017/', serverSelectionTimeoutMS=3000)
        client.admin.command('ping')
        client.close()
        print("✓ MongoDB连接正常")
        return True
    except Exception as e:
        print(f"✗ MongoDB连接失败: {e}")
        print("请确保MongoDB服务正在运行")
        return False

def setup_database():
    """设置数据库"""
    print("\n正在设置数据库...")
    try:
        result = subprocess.run([sys.executable, 'database_setup.py'], 
                              capture_output=True, text=True, encoding='utf-8')
        if result.returncode == 0:
            print("✓ 数据库设置完成")
            return True
        else:
            print(f"✗ 数据库设置失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"✗ 数据库设置异常: {e}")
        return False

def start_api_server():
    """启动API服务器"""
    print("\n启动API服务器...")
    try:
        # 在新线程中启动Flask服务器
        def run_server():
            subprocess.run([sys.executable, 'api_server.py'])
        
        server_thread = threading.Thread(target=run_server, daemon=True)
        server_thread.start()
        
        # 等待服务器启动
        time.sleep(3)
        
        # 检查服务器是否启动成功
        import requests
        try:
            response = requests.get('http://localhost:5000/health', timeout=5)
            if response.status_code == 200:
                print("✓ API服务器启动成功")
                print("  访问地址: http://localhost:5000")
                return True
            else:
                print(f"✗ API服务器响应异常: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"✗ API服务器连接失败: {e}")
            return False
            
    except Exception as e:
        print(f"✗ API服务器启动异常: {e}")
        return False

def start_data_import(data_dir, workers):
    """启动数据导入"""
    print(f"\n开始导入数据...")
    print(f"数据目录: {data_dir}")
    print(f"并行线程: {workers}")
    
    try:
        # 启动数据导入进程
        cmd = [sys.executable, 'data_importer.py', 
               '--data-dir', data_dir, 
               '--workers', str(workers)]
        
        print("执行命令:", ' '.join(cmd))
        
        # 实时显示输出
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, 
                                 stderr=subprocess.STDOUT, 
                                 text=True, encoding='utf-8',
                                 bufsize=1, universal_newlines=True)
        
        for line in process.stdout:
            print(line.rstrip())
        
        process.wait()
        
        if process.returncode == 0:
            print("✓ 数据导入完成")
            return True
        else:
            print(f"✗ 数据导入失败，退出码: {process.returncode}")
            return False
            
    except Exception as e:
        print(f"✗ 数据导入异常: {e}")
        return False

def analyze_data_structure():
    """分析数据结构"""
    print("\n分析数据结构...")
    try:
        result = subprocess.run([sys.executable, 'analyze_data.py'], 
                              capture_output=True, text=True, encoding='utf-8')
        if result.returncode == 0:
            print("✓ 数据结构分析完成")
            print(result.stdout)
            return True
        else:
            print(f"✗ 数据结构分析失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"✗ 数据结构分析异常: {e}")
        return False

def check_dependencies():
    """检查依赖"""
    print("检查Python依赖...")
    
    required_packages = [
        'pymongo', 'pandas', 'flask', 'tqdm', 'requests'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package} (缺失)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺失的包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='裁判文书导入系统启动工具')
    parser.add_argument('--mode', choices=['setup', 'import', 'server', 'all'], 
                       default='all', help='运行模式')
    parser.add_argument('--data-dir', default='裁判文书', help='数据目录路径')
    parser.add_argument('--workers', type=int, default=4, help='导入并行线程数')
    parser.add_argument('--skip-analysis', action='store_true', help='跳过数据结构分析')
    
    args = parser.parse_args()
    
    print("="*60)
    print("裁判文书导入系统启动工具")
    print("="*60)
    
    # 检查依赖
    if not check_dependencies():
        return False
    
    # 检查MongoDB
    if not check_mongodb():
        return False
    
    success = True
    
    if args.mode in ['setup', 'all']:
        # 设置数据库
        if not setup_database():
            success = False
        
        # 分析数据结构（可选）
        if not args.skip_analysis:
            if not analyze_data_structure():
                print("警告: 数据结构分析失败，但继续执行")
    
    if args.mode in ['server', 'all'] and success:
        # 启动API服务器
        if not start_api_server():
            success = False
    
    if args.mode in ['import', 'all'] and success:
        # 检查数据目录
        if not os.path.exists(args.data_dir):
            print(f"✗ 数据目录不存在: {args.data_dir}")
            return False
        
        # 启动数据导入
        if not start_data_import(args.data_dir, args.workers):
            success = False
    
    if success:
        print("\n" + "="*60)
        print("系统启动完成！")
        print("="*60)
        
        if args.mode in ['server', 'all']:
            print("监控界面: http://localhost:5000")
            print("API文档:")
            print("  - 统计信息: http://localhost:5000/api/statistics")
            print("  - 导入进度: http://localhost:5000/api/progress")
            print("  - 文档数量: http://localhost:5000/api/documents/count")
            print("  - 地区统计: http://localhost:5000/api/statistics/region")
            print("  - 案件类型统计: http://localhost:5000/api/statistics/case_type")
        
        if args.mode == 'server':
            print("\n按 Ctrl+C 停止服务器")
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n服务器已停止")
        
        return True
    else:
        print("\n" + "="*60)
        print("系统启动失败！")
        print("="*60)
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
