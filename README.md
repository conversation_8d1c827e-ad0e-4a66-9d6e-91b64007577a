# 裁判文书数据导入系统

这是一个用于将裁判文书数据批量导入MongoDB数据库并提供实时监控的完整系统。

## 功能特性

- 🚀 **批量数据导入**: 支持递归解压ZIP文件，自动识别CSV格式数据
- 📊 **实时进度监控**: Web界面实时显示导入进度和统计信息
- 🔍 **数据结构分析**: 自动分析数据格式和字段结构
- 💾 **MongoDB存储**: 优化的数据库设计，包含索引和验证规则
- 🔄 **并发处理**: 多线程并行处理，提高导入效率
- 📈 **统计分析**: 提供地区分布、案件类型等统计信息
- 🛡️ **错误处理**: 完善的错误处理和重复数据检测

## 系统要求

- Python 3.7+
- MongoDB 4.0+
- 至少4GB内存（推荐8GB+）

## 安装步骤

### 1. 创建虚拟环境

```bash
python -m venv venv
# Windows
.\venv\Scripts\activate
# Linux/Mac
source venv/bin/activate
```

### 2. 安装依赖

```bash
pip install pymongo pandas flask tqdm requests python-dotenv
```

### 3. 启动MongoDB

确保MongoDB服务正在运行：

```bash
# Windows (如果安装为服务)
net start MongoDB

# Linux/Mac
sudo systemctl start mongod
# 或
mongod --dbpath /path/to/your/db
```

## 使用方法

### 快速启动（推荐）

使用启动脚本一键运行整个系统：

```bash
python start_system.py
```

这将自动执行以下步骤：
1. 检查依赖和MongoDB连接
2. 设置数据库和集合
3. 分析数据结构
4. 启动Web监控界面
5. 开始数据导入

### 自定义参数

```bash
# 指定数据目录和线程数
python start_system.py --data-dir "裁判文书" --workers 8

# 只启动监控服务器
python start_system.py --mode server

# 只进行数据导入
python start_system.py --mode import --data-dir "裁判文书" --workers 4

# 只设置数据库
python start_system.py --mode setup
```

### 分步执行

如果需要分步执行，可以按以下顺序运行：

#### 1. 数据库设置

```bash
python database_setup.py
```

#### 2. 数据结构分析（可选）

```bash
python analyze_data.py
```

#### 3. 启动监控界面

```bash
python api_server.py
```

然后在浏览器中访问 http://localhost:5000

#### 4. 数据导入

```bash
python data_importer.py --data-dir "裁判文书" --workers 4
```

## 监控界面

启动系统后，访问 http://localhost:5000 查看：

- 📊 **实时统计**: 总文档数、处理进度、成功率等
- 📋 **文件详情**: 每个文件的处理状态和错误信息
- 🔄 **自动刷新**: 可选择30秒自动刷新数据
- 📈 **进度条**: 可视化显示整体进度

## API接口

系统提供以下REST API接口：

- `GET /api/statistics` - 获取总体统计信息
- `GET /api/progress` - 获取文件处理进度
- `GET /api/documents/count` - 获取文档总数
- `GET /api/documents/recent` - 获取最近导入的文档
- `GET /api/statistics/region` - 获取地区分布统计
- `GET /api/statistics/case_type` - 获取案件类型统计
- `GET /health` - 健康检查

## 数据库结构

### 集合说明

1. **documents** - 裁判文书主集合
   - 存储所有裁判文书数据
   - 包含案号、地区、类型、全文等字段
   - 建立了多个索引优化查询性能

2. **import_progress** - 导入进度集合
   - 跟踪每个文件的处理状态
   - 记录开始时间、结束时间、错误信息等

3. **statistics** - 统计信息集合
   - 存储各种统计数据
   - 包括总数、分布情况等

### 主要字段

- `案号` - 案件编号（唯一索引）
- `所属地区` - 案件所属地区
- `案件类型` - 案件类型
- `审理程序` - 审理程序
- `案件名称` - 案件名称
- `案由` - 案由（数组格式）
- `全文` - 裁判文书全文（支持全文搜索）
- `裁判日期` - 裁判日期
- `导入时间` - 数据导入时间
- `文件来源` - 数据来源文件

## 性能优化

### 导入性能

- 使用多线程并行处理文件
- 批量插入数据（默认1000条/批）
- 跳过重复数据，避免重复导入
- 内存优化，分批处理大文件

### 查询性能

- 为常用字段建立索引
- 支持全文搜索
- 复合索引优化复杂查询

## 故障排除

### 常见问题

1. **MongoDB连接失败**
   - 检查MongoDB服务是否启动
   - 确认连接字符串正确
   - 检查防火墙设置

2. **内存不足**
   - 减少并行线程数（--workers参数）
   - 增加系统内存
   - 分批处理数据

3. **编码错误**
   - 系统自动尝试多种编码（UTF-8, GBK, GB2312）
   - 检查源文件编码格式

4. **文件格式错误**
   - 确认CSV文件格式正确
   - 检查压缩文件是否损坏

### 日志查看

系统会输出详细的日志信息，包括：
- 处理进度
- 错误信息
- 性能统计

## 配置选项

可以通过环境变量配置系统参数：

```bash
# MongoDB连接字符串
export MONGODB_CONNECTION_STRING="mongodb://localhost:27017/"

# 数据库名称
export MONGODB_DATABASE_NAME="judicial_documents"
```

## 扩展功能

系统设计为可扩展的架构，可以轻松添加：

- 更多数据源支持
- 高级搜索功能
- 数据导出功能
- 用户权限管理
- 分布式处理

## 许可证

本项目采用MIT许可证。

## 技术支持

如有问题，请检查：
1. 系统要求是否满足
2. 依赖是否正确安装
3. MongoDB是否正常运行
4. 数据文件格式是否正确

---

**注意**: 首次运行可能需要较长时间，取决于数据量大小。建议在服务器环境中运行以获得最佳性能。
