#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全量数据导入脚本
专门用于导入所有裁判文书数据，按年月组织
"""

import os
import sys
import zipfile
import pandas as pd
import json
import ast
import re
import shutil
from datetime import datetime
from pathlib import Path
from pymongo import MongoClient
from pymongo.errors import DuplicateKeyError, BulkWriteError
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from tqdm import tqdm

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FullDataImporter:
    def __init__(self, connection_string="mongodb://localhost:27017/", db_name="judicial_documents"):
        """
        初始化全量数据导入器
        """
        self.connection_string = connection_string
        self.db_name = db_name
        self.client = None
        self.db = None
        self.lock = threading.Lock()
        
        # 统计信息
        self.stats = {
            'total_files': 0,
            'processed_files': 0,
            'failed_files': 0,
            'total_records': 0,
            'imported_records': 0,
            'duplicate_records': 0,
            'error_records': 0,
            'extracted_files': 0
        }
        
        # 创建临时解压目录
        self.temp_dir = "temp_extracted"
        os.makedirs(self.temp_dir, exist_ok=True)
    
    def connect(self):
        """连接到MongoDB"""
        try:
            self.client = MongoClient(self.connection_string)
            self.db = self.client[self.db_name]
            # 测试连接
            self.client.admin.command('ping')
            logger.info(f"成功连接到MongoDB数据库: {self.db_name}")
            return True
        except Exception as e:
            logger.error(f"连接MongoDB失败: {e}")
            return False
    
    def extract_year_month_from_filename(self, filename):
        """从文件名中提取年月信息"""
        # 尝试多种模式匹配年月
        patterns = [
            r'(\d{4})年(\d{1,2})月',  # 2021年01月
            r'(\d{4})年?(\d{1,2})',   # 2021年1 或 20211
            r'(\d{4})-(\d{1,2})',     # 2021-01
            r'(\d{4})_(\d{1,2})',     # 2021_01
            r'(\d{4})\.(\d{1,2})',    # 2021.01
            r'(\d{4})(\d{2})',        # 202101
        ]
        
        for pattern in patterns:
            match = re.search(pattern, filename)
            if match:
                year = int(match.group(1))
                month = int(match.group(2))
                if 2000 <= year <= 2030 and 1 <= month <= 12:
                    return year, month
        
        # 如果没有找到月份，尝试只匹配年份
        year_match = re.search(r'(\d{4})', filename)
        if year_match:
            year = int(year_match.group(1))
            if 2000 <= year <= 2030:
                return year, None
        
        return None, None
    
    def extract_all_archives(self, data_dir="裁判文书"):
        """递归解压所有压缩文件"""
        print(f"开始解压 {data_dir} 目录下的所有压缩文件...")
        
        # 收集所有压缩文件
        archive_files = []
        for root, dirs, files in os.walk(data_dir):
            for file in files:
                if file.endswith(('.zip', '.rar', '.7z')):
                    archive_files.append(os.path.join(root, file))
        
        print(f"找到 {len(archive_files)} 个压缩文件")
        
        extracted_count = 0
        
        with tqdm(total=len(archive_files), desc="解压文件") as pbar:
            for archive_path in archive_files:
                try:
                    # 创建解压目录
                    archive_name = os.path.splitext(os.path.basename(archive_path))[0]
                    extract_dir = os.path.join(self.temp_dir, archive_name)
                    os.makedirs(extract_dir, exist_ok=True)
                    
                    # 解压文件
                    if archive_path.endswith('.zip'):
                        with zipfile.ZipFile(archive_path, 'r') as zip_ref:
                            zip_ref.extractall(extract_dir)
                        extracted_count += 1
                        logger.info(f"解压完成: {archive_path}")
                    
                except Exception as e:
                    logger.error(f"解压失败 {archive_path}: {e}")
                
                pbar.update(1)
        
        self.stats['extracted_files'] = extracted_count
        print(f"解压完成，共解压 {extracted_count} 个文件")
        
        return extracted_count > 0
    
    def clean_data(self, df, source_file=""):
        """清洗数据并添加年月信息"""
        try:
            # 处理案由字段
            if '案由' in df.columns:
                def parse_case_cause(cause_str):
                    if pd.isna(cause_str) or cause_str == '':
                        return []
                    try:
                        if isinstance(cause_str, str) and cause_str.startswith('['):
                            return ast.literal_eval(cause_str)
                        else:
                            return [str(cause_str)]
                    except:
                        return [str(cause_str)]
                
                df['案由'] = df['案由'].apply(parse_case_cause)
            
            # 处理裁判日期
            if '裁判日期' in df.columns:
                def parse_date(date_str):
                    if pd.isna(date_str):
                        return None
                    return str(date_str) if date_str != '' else None
                
                df['裁判日期'] = df['裁判日期'].apply(parse_date)
            
            # 从文件名提取年月信息
            year, month = self.extract_year_month_from_filename(source_file)
            if year:
                df['数据年份'] = year
                logger.info(f"从文件名 {source_file} 提取年份: {year}")
            if month:
                df['数据月份'] = month
                logger.info(f"从文件名 {source_file} 提取月份: {month}")
            
            # 添加导入时间和文件来源
            df['导入时间'] = datetime.now()
            df['文件来源'] = os.path.basename(source_file)
            
            # 填充空值
            df = df.fillna('')
            
            # 确保必需字段不为空
            required_fields = ['案号', '所属地区', '案件类型', '审理程序']
            original_count = len(df)
            for field in required_fields:
                if field in df.columns:
                    df = df[df[field] != '']
            
            if len(df) < original_count:
                logger.info(f"数据清洗：从 {original_count} 条记录过滤到 {len(df)} 条记录")
            
            return df
            
        except Exception as e:
            logger.error(f"数据清洗失败: {e}")
            return df
    
    def bulk_insert_records(self, records, batch_size=1000):
        """批量插入记录"""
        imported = 0
        duplicates = 0
        errors = 0
        
        try:
            # 分批插入
            for i in range(0, len(records), batch_size):
                batch = records[i:i + batch_size]
                
                try:
                    result = self.db['documents'].insert_many(batch, ordered=False)
                    imported += len(result.inserted_ids)
                    
                except BulkWriteError as bwe:
                    # 处理批量写入错误
                    for error in bwe.details['writeErrors']:
                        if error['code'] == 11000:  # 重复键错误
                            duplicates += 1
                        else:
                            errors += 1
                    
                    # 计算成功插入的数量
                    imported += bwe.details['nInserted']
                
                except Exception as e:
                    logger.error(f"批量插入失败: {e}")
                    errors += len(batch)
        
        except Exception as e:
            logger.error(f"批量插入过程失败: {e}")
            errors = len(records)
        
        return imported, duplicates, errors
    
    def process_csv_files(self):
        """处理所有解压出来的CSV文件"""
        print("开始处理CSV文件...")
        
        # 收集所有CSV文件
        csv_files = []
        for root, dirs, files in os.walk(self.temp_dir):
            for file in files:
                if file.endswith('.csv'):
                    csv_files.append(os.path.join(root, file))
        
        print(f"找到 {len(csv_files)} 个CSV文件")
        self.stats['total_files'] = len(csv_files)
        
        if not csv_files:
            print("没有找到CSV文件")
            return False
        
        # 处理每个CSV文件
        with tqdm(total=len(csv_files), desc="处理CSV文件") as pbar:
            for csv_file in csv_files:
                try:
                    # 尝试不同的编码读取CSV
                    encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
                    df = None
                    
                    for encoding in encodings:
                        try:
                            df = pd.read_csv(csv_file, encoding=encoding)
                            break
                        except Exception:
                            continue
                    
                    if df is None:
                        logger.error(f"无法读取CSV文件: {csv_file}")
                        self.stats['failed_files'] += 1
                        continue
                    
                    # 数据清洗
                    df = self.clean_data(df, csv_file)
                    
                    if df.empty:
                        logger.warning(f"CSV文件清洗后为空: {csv_file}")
                        self.stats['processed_files'] += 1
                        continue
                    
                    # 转换为字典列表并插入
                    records = df.to_dict('records')
                    imported, duplicates, errors = self.bulk_insert_records(records)
                    
                    # 更新统计
                    with self.lock:
                        self.stats['processed_files'] += 1
                        self.stats['total_records'] += len(records)
                        self.stats['imported_records'] += imported
                        self.stats['duplicate_records'] += duplicates
                        self.stats['error_records'] += errors
                    
                    logger.info(f"处理完成 {csv_file}: 导入 {imported}, 重复 {duplicates}, 错误 {errors}")
                    
                except Exception as e:
                    logger.error(f"处理CSV文件失败 {csv_file}: {e}")
                    self.stats['failed_files'] += 1
                
                pbar.update(1)
        
        return True
    
    def cleanup_temp_files(self):
        """清理临时文件"""
        try:
            if os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
                print(f"清理临时目录: {self.temp_dir}")
        except Exception as e:
            logger.error(f"清理临时文件失败: {e}")
    
    def update_final_statistics(self):
        """更新最终统计信息"""
        try:
            # 重新计算总文档数
            total_docs = self.db['documents'].count_documents({})
            
            # 更新统计信息
            current_time = datetime.now()
            
            self.db['statistics'].update_one(
                {'stat_type': 'total_documents'},
                {'$set': {'value': total_docs, 'update_time': current_time}},
                upsert=True
            )
            
            # 更新导入进度统计
            import_stats = {
                'total_files': self.stats['total_files'],
                'processed_files': self.stats['processed_files'],
                'failed_files': self.stats['failed_files'],
                'success_rate': (self.stats['processed_files'] / self.stats['total_files'] * 100) if self.stats['total_files'] > 0 else 0,
                'total_records': self.stats['total_records'],
                'imported_records': self.stats['imported_records'],
                'duplicate_records': self.stats['duplicate_records'],
                'error_records': self.stats['error_records'],
                'extracted_files': self.stats['extracted_files']
            }
            
            self.db['statistics'].update_one(
                {'stat_type': 'import_progress'},
                {'$set': {'value': import_stats, 'update_time': current_time}},
                upsert=True
            )
            
            logger.info("最终统计信息已更新")
            
        except Exception as e:
            logger.error(f"更新统计信息失败: {e}")
    
    def print_final_stats(self):
        """打印最终统计信息"""
        print("\n" + "="*60)
        print("全量数据导入完成统计")
        print("="*60)
        print(f"解压文件数: {self.stats['extracted_files']}")
        print(f"总CSV文件数: {self.stats['total_files']}")
        print(f"成功处理: {self.stats['processed_files']}")
        print(f"处理失败: {self.stats['failed_files']}")
        print(f"成功率: {(self.stats['processed_files'] / self.stats['total_files'] * 100):.2f}%" if self.stats['total_files'] > 0 else "0%")
        print(f"总记录数: {self.stats['total_records']}")
        print(f"导入记录: {self.stats['imported_records']}")
        print(f"重复记录: {self.stats['duplicate_records']}")
        print(f"错误记录: {self.stats['error_records']}")
        print("="*60)
    
    def run_full_import(self, data_dir="裁判文书"):
        """运行全量导入"""
        if not self.connect():
            return False
        
        try:
            print("开始全量数据导入...")
            
            # 1. 解压所有压缩文件
            if not self.extract_all_archives(data_dir):
                print("解压失败，终止导入")
                return False
            
            # 2. 处理所有CSV文件
            if not self.process_csv_files():
                print("处理CSV文件失败")
                return False
            
            # 3. 更新最终统计信息
            self.update_final_statistics()
            
            # 4. 打印统计信息
            self.print_final_stats()
            
            return True
            
        except Exception as e:
            logger.error(f"全量导入过程中发生错误: {e}")
            return False
        finally:
            # 清理临时文件
            self.cleanup_temp_files()
            
            if self.client:
                self.client.close()

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='裁判文书全量数据导入工具')
    parser.add_argument('--data-dir', default='裁判文书', help='数据目录路径')
    parser.add_argument('--connection', default='mongodb://localhost:27017/', help='MongoDB连接字符串')
    parser.add_argument('--database', default='judicial_documents', help='数据库名称')
    
    args = parser.parse_args()
    
    importer = FullDataImporter(args.connection, args.database)
    
    print(f"开始全量导入数据...")
    print(f"数据目录: {args.data_dir}")
    print(f"数据库: {args.database}")
    
    success = importer.run_full_import(args.data_dir)
    
    if success:
        print("全量数据导入完成！")
    else:
        print("全量数据导入失败！")

if __name__ == "__main__":
    main()
