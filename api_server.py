#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Flask API服务器
提供实时的数据导入进度统计信息
"""

import os
from flask import Flask, jsonify, render_template_string
from pymongo import MongoClient
from datetime import datetime, timedelta
import logging
from functools import wraps
import json

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# MongoDB配置
MONGODB_CONNECTION = os.getenv('MONGODB_CONNECTION_STRING', 'mongodb://localhost:27017/')
DATABASE_NAME = os.getenv('MONGODB_DATABASE_NAME', 'judicial_documents')

def get_db_connection():
    """获取数据库连接"""
    try:
        client = MongoClient(MONGODB_CONNECTION)
        db = client[DATABASE_NAME]
        return client, db
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        return None, None

def handle_db_errors(f):
    """数据库错误处理装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            return f(*args, **kwargs)
        except Exception as e:
            logger.error(f"API错误: {e}")
            return jsonify({'error': str(e)}), 500
    return decorated_function

@app.route('/')
def index():
    """主页面"""
    html_template = """
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>裁判文书导入进度监控</title>
        <style>
            body {
                font-family: 'Microsoft YaHei', Arial, sans-serif;
                margin: 0;
                padding: 20px;
                background-color: #f5f5f5;
            }
            .container {
                max-width: 1200px;
                margin: 0 auto;
                background: white;
                padding: 20px;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            h1 {
                color: #333;
                text-align: center;
                margin-bottom: 30px;
            }
            .stats-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 20px;
                margin-bottom: 30px;
            }
            .stat-card {
                background: #f8f9fa;
                padding: 20px;
                border-radius: 6px;
                border-left: 4px solid #007bff;
            }
            .stat-card h3 {
                margin: 0 0 10px 0;
                color: #495057;
                font-size: 14px;
                text-transform: uppercase;
            }
            .stat-card .value {
                font-size: 24px;
                font-weight: bold;
                color: #007bff;
            }
            .progress-bar {
                width: 100%;
                height: 20px;
                background-color: #e9ecef;
                border-radius: 10px;
                overflow: hidden;
                margin: 10px 0;
            }
            .progress-fill {
                height: 100%;
                background-color: #28a745;
                transition: width 0.3s ease;
            }
            .refresh-btn {
                background: #007bff;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 16px;
                margin-bottom: 20px;
            }
            .refresh-btn:hover {
                background: #0056b3;
            }
            .table-container {
                overflow-x: auto;
            }
            table {
                width: 100%;
                border-collapse: collapse;
                margin-top: 20px;
            }
            th, td {
                padding: 12px;
                text-align: left;
                border-bottom: 1px solid #dee2e6;
            }
            th {
                background-color: #f8f9fa;
                font-weight: 600;
            }
            .status-completed { color: #28a745; }
            .status-processing { color: #ffc107; }
            .status-failed { color: #dc3545; }
            .status-pending { color: #6c757d; }
            .auto-refresh {
                margin-left: 10px;
                font-size: 14px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>裁判文书导入进度监控</h1>
            
            <button class="refresh-btn" onclick="refreshData()">刷新数据</button>
            <label class="auto-refresh">
                <input type="checkbox" id="autoRefresh" onchange="toggleAutoRefresh()"> 自动刷新 (30秒)
            </label>
            
            <div class="stats-grid" id="statsGrid">
                <!-- 统计卡片将通过JavaScript动态生成 -->
            </div>
            
            <div class="table-container">
                <h2>文件处理详情</h2>
                <table id="progressTable">
                    <thead>
                        <tr>
                            <th>文件名</th>
                            <th>状态</th>
                            <th>开始时间</th>
                            <th>结束时间</th>
                            <th>处理记录数</th>
                            <th>错误信息</th>
                        </tr>
                    </thead>
                    <tbody id="progressTableBody">
                        <!-- 表格内容将通过JavaScript动态生成 -->
                    </tbody>
                </table>
            </div>
        </div>

        <script>
            let autoRefreshInterval = null;

            function refreshData() {
                Promise.all([
                    fetch('/api/statistics').then(r => r.json()),
                    fetch('/api/progress').then(r => r.json())
                ]).then(([stats, progress]) => {
                    updateStatsGrid(stats);
                    updateProgressTable(progress);
                }).catch(error => {
                    console.error('获取数据失败:', error);
                });
            }

            function updateStatsGrid(stats) {
                const grid = document.getElementById('statsGrid');
                const importStats = stats.import_progress || {};
                
                const cards = [
                    { title: '总文档数', value: stats.total_documents || 0 },
                    { title: '总文件数', value: importStats.total_files || 0 },
                    { title: '已处理文件', value: importStats.processed_files || 0 },
                    { title: '失败文件', value: importStats.failed_files || 0 },
                    { title: '成功率', value: (importStats.success_rate || 0).toFixed(2) + '%' },
                    { title: '导入记录数', value: importStats.imported_records || 0 },
                    { title: '重复记录数', value: importStats.duplicate_records || 0 },
                    { title: '错误记录数', value: importStats.error_records || 0 }
                ];

                grid.innerHTML = cards.map(card => `
                    <div class="stat-card">
                        <h3>${card.title}</h3>
                        <div class="value">${card.value}</div>
                        ${card.title === '成功率' ? `
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: ${parseFloat(card.value)}%"></div>
                            </div>
                        ` : ''}
                    </div>
                `).join('');
            }

            function updateProgressTable(progress) {
                const tbody = document.getElementById('progressTableBody');
                
                tbody.innerHTML = progress.map(item => `
                    <tr>
                        <td>${item.file_name}</td>
                        <td><span class="status-${item.status}">${getStatusText(item.status)}</span></td>
                        <td>${formatDateTime(item.start_time)}</td>
                        <td>${formatDateTime(item.end_time)}</td>
                        <td>${item.processed_records || 0}</td>
                        <td>${item.error_message || ''}</td>
                    </tr>
                `).join('');
            }

            function getStatusText(status) {
                const statusMap = {
                    'pending': '等待中',
                    'processing': '处理中',
                    'completed': '已完成',
                    'failed': '失败'
                };
                return statusMap[status] || status;
            }

            function formatDateTime(dateStr) {
                if (!dateStr) return '';
                return new Date(dateStr).toLocaleString('zh-CN');
            }

            function toggleAutoRefresh() {
                const checkbox = document.getElementById('autoRefresh');
                if (checkbox.checked) {
                    autoRefreshInterval = setInterval(refreshData, 30000);
                } else {
                    if (autoRefreshInterval) {
                        clearInterval(autoRefreshInterval);
                        autoRefreshInterval = null;
                    }
                }
            }

            // 页面加载时获取初始数据
            document.addEventListener('DOMContentLoaded', refreshData);
        </script>
    </body>
    </html>
    """
    return render_template_string(html_template)

@app.route('/api/statistics')
@handle_db_errors
def get_statistics():
    """获取统计信息"""
    client, db = get_db_connection()
    if db is None:
        return jsonify({'error': '数据库连接失败'}), 500
    
    try:
        stats = {}
        
        # 获取所有统计信息
        for stat_doc in db['statistics'].find():
            stats[stat_doc['stat_type']] = stat_doc['value']
        
        return jsonify(stats)
    
    finally:
        if client:
            client.close()

@app.route('/api/progress')
@handle_db_errors
def get_progress():
    """获取导入进度"""
    client, db = get_db_connection()
    if db is None:
        return jsonify({'error': '数据库连接失败'}), 500
    
    try:
        # 获取最近的进度记录
        progress_docs = list(db['import_progress'].find().sort('start_time', -1).limit(100))
        
        # 转换ObjectId为字符串
        for doc in progress_docs:
            doc['_id'] = str(doc['_id'])
            # 转换日期为ISO格式字符串
            if 'start_time' in doc:
                doc['start_time'] = doc['start_time'].isoformat() if doc['start_time'] else None
            if 'end_time' in doc:
                doc['end_time'] = doc['end_time'].isoformat() if doc['end_time'] else None
            if 'update_time' in doc:
                doc['update_time'] = doc['update_time'].isoformat() if doc['update_time'] else None
        
        return jsonify(progress_docs)
    
    finally:
        if client:
            client.close()

@app.route('/api/documents/count')
@handle_db_errors
def get_document_count():
    """获取文档总数"""
    client, db = get_db_connection()
    if db is None:
        return jsonify({'error': '数据库连接失败'}), 500
    
    try:
        count = db['documents'].count_documents({})
        return jsonify({'count': count})
    
    finally:
        if client:
            client.close()

@app.route('/api/documents/recent')
@handle_db_errors
def get_recent_documents():
    """获取最近导入的文档"""
    client, db = get_db_connection()
    if db is None:
        return jsonify({'error': '数据库连接失败'}), 500
    
    try:
        # 获取最近导入的10个文档
        recent_docs = list(db['documents'].find(
            {},
            {'案号': 1, '案件名称': 1, '所属地区': 1, '案件类型': 1, '导入时间': 1}
        ).sort('导入时间', -1).limit(10))
        
        # 转换ObjectId和日期
        for doc in recent_docs:
            doc['_id'] = str(doc['_id'])
            if '导入时间' in doc:
                doc['导入时间'] = doc['导入时间'].isoformat() if doc['导入时间'] else None
        
        return jsonify(recent_docs)
    
    finally:
        if client:
            client.close()

@app.route('/api/statistics/region')
@handle_db_errors
def get_region_statistics():
    """获取地区分布统计"""
    client, db = get_db_connection()
    if db is None:
        return jsonify({'error': '数据库连接失败'}), 500
    
    try:
        # 使用聚合管道统计地区分布
        pipeline = [
            {'$group': {'_id': '$所属地区', 'count': {'$sum': 1}}},
            {'$sort': {'count': -1}},
            {'$limit': 20}
        ]
        
        region_stats = list(db['documents'].aggregate(pipeline))
        
        # 格式化结果
        result = [{'region': item['_id'], 'count': item['count']} for item in region_stats]
        
        return jsonify(result)
    
    finally:
        if client:
            client.close()

@app.route('/api/statistics/case_type')
@handle_db_errors
def get_case_type_statistics():
    """获取案件类型分布统计"""
    client, db = get_db_connection()
    if db is None:
        return jsonify({'error': '数据库连接失败'}), 500
    
    try:
        # 使用聚合管道统计案件类型分布
        pipeline = [
            {'$group': {'_id': '$案件类型', 'count': {'$sum': 1}}},
            {'$sort': {'count': -1}},
            {'$limit': 20}
        ]
        
        case_type_stats = list(db['documents'].aggregate(pipeline))
        
        # 格式化结果
        result = [{'case_type': item['_id'], 'count': item['count']} for item in case_type_stats]
        
        return jsonify(result)
    
    finally:
        if client:
            client.close()

@app.route('/health')
def health_check():
    """健康检查"""
    client, db = get_db_connection()
    if db is None:
        return jsonify({'status': 'unhealthy', 'message': '数据库连接失败'}), 500

    try:
        # 测试数据库连接
        db.command('ping')
        client.close()
        return jsonify({'status': 'healthy', 'timestamp': datetime.now().isoformat()})
    except Exception as e:
        return jsonify({'status': 'unhealthy', 'message': str(e)}), 500

if __name__ == '__main__':
    print("启动Flask API服务器...")
    print(f"数据库: {DATABASE_NAME}")
    print(f"访问地址: http://localhost:5000")
    
    app.run(host='0.0.0.0', port=5000, debug=True)
